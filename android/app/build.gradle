plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
}

android {
    namespace = "com.appinventor"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_21
    }

    defaultConfig {
        // Base applicationId - will be overridden by flavor-specific applicationId
        applicationId = "com.gg.gleezy"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName

        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }
    }
    flavorDimensions "app"

    productFlavors {
        prod {
            dimension "app"
            applicationId = "com.gg.gleezy"
        }

        abGrammar {
            dimension "app"
            applicationId = "appinventor.ai_liveschoolbd.GrammarTerms"
            versionName = "2.0.4"
        }

        abIcon {
            dimension "app"
            applicationId = "com.icon.plus"
            versionName = "2.0.4"
        }
    }

    signingConfigs {
        google {
            keyAlias 'upload'
            keyPassword 'kennedy166@'
            storeFile file('google.jks')
            storePassword 'kennedy166@'
        }
    }

    buildTypes {
        release {
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.google
            minifyEnabled false
            shrinkResources false
        }
        debug {
            signingConfig signingConfigs.google
        }
    }

    packagingOptions {
        // 只包含特定的so库
        pickFirsts.add("**/libmhjtmi.so")
        // 排除其他所有so库
        excludes.add("**/*.so")
        excludes.remove("**/libmhjtmi.so") // 确保不排除我们需要的so
        exclude '**/flutter_assets/**'
    }
}

android.applicationVariants.all { variant ->
    variant.assembleProvider.get().doFirst {
        println("删除assets文件夹下的所有zip文件$variant.mergeAssets.outputDir")
        // 列出当前文件夹下的所有文件
        def files = variant.mergeAssets.outputDir.get().asFile.listFiles()

        for (File file : files) {
            println("file.getName():" + file.getName())
        }
        delete(fileTree(dir: variant.mergeAssets.outputDir, includes: ['flutter_assets/**/*']))
    }
}

flutter {
    source = "../.."
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.5'
    implementation "androidx.work:work-runtime-ktx:2.8.1"
}