#!/bin/bash

# 显示帮助信息
show_help() {
  echo "用法: ./build.sh [打包类型] [--flavor=FLAVOR] [--channel=CHANNEL] [--google-services=PATH]"
  echo ""
  echo "打包类型:"
  echo "  apk        构建 APK 包 (默认)"
  echo "  aab        构建 App Bundle 包"
  echo "  appbundle  构建 App Bundle 包 (aab 的别名)"
  echo "  all        同时构建 APK 和 App Bundle"
  echo "  help       显示帮助信息"
  echo ""
  echo "参数:"
  echo "  --flavor=FLAVOR            指定构建的 flavor (prod, google 等)"
  echo "  --channel=CHANNEL          指定构建的 channel (默认与 flavor 相同)"
  echo ""
  echo "功能:"
  echo "  构建完成后会生成 patch.zip 文件，包含 assets目录、AndroidManifest.xml 和 libapp.so"
  echo ""
  echo "示例:"
  echo "  ./build.sh                  # 构建 APK，使用默认 flavor"
  echo "  ./build.sh apk              # 构建 APK，使用默认 flavor"
  echo "  ./build.sh aab --flavor=prod # 构建 prod flavor 的 App Bundle"
  echo "  ./build.sh all --flavor=google # 同时构建 google flavor 的 APK 和 App Bundle"
  echo "  ./build.sh apk --flavor=prod --channel=test # 构建 prod flavor 但使用 test channel"
  exit 0
}

# 默认打包类型为 apk
BUILD_TYPE="apk"

# 默认 flavor 为 prod
FLAVOR="prod"

# 默认 channel 为空，如果不指定则使用 flavor 的值
CHANNEL=""

# 处理命令行参数
for arg in "$@"; do
  case "$arg" in
    help|--help|-h)
      show_help
      ;;
    --flavor=*)
      FLAVOR="${arg#*=}"
      ;;
    --channel=*)
      CHANNEL="${arg#*=}"
      ;;
    apk|aab|appbundle|all)
      # 使用参数作为构建类型
      BUILD_TYPE="$arg"
      ;;
  esac
done

# 如果没有指定 channel，则使用 flavor 的值
if [ -z "$CHANNEL" ]; then
  CHANNEL="$FLAVOR"
  echo "未指定 channel，使用 flavor 的值: $CHANNEL"
fi

# 从 Android flavor 中读取 versionName
get_version_name_from_flavor() {
  local flavor=$1
  local gradle_file="android/app/build.gradle"

  # 首先尝试从对应的 flavor 中读取 versionName
  local flavor_version=$(awk -v flavor="$flavor" '
    BEGIN { in_flavor = 0; in_product_flavors = 0 }
    /productFlavors/ { in_product_flavors = 1 }
    in_product_flavors && $0 ~ flavor " {" { in_flavor = 1; next }
    in_flavor && /versionName/ {
      gsub(/["\047]/, "", $3);
      print $3;
      exit
    }
    in_flavor && /}/ { in_flavor = 0 }
    /^}/ && in_product_flavors { in_product_flavors = 0 }
  ' "$gradle_file")

  # 如果 flavor 中没有定义 versionName，则使用 pubspec.yaml 中的版本
  if [ -z "$flavor_version" ]; then
    flavor_version=$(grep 'version:' pubspec.yaml | sed 's/version: //' | cut -d+ -f1 | tr -d ' ')
  fi

  echo "$flavor_version"
}

# 提取版本名称和构建号
VERSION_NAME=$(get_version_name_from_flavor "$FLAVOR")
BUILD_NUMBER=$(grep 'version:' pubspec.yaml | sed 's/version: //' | cut -d+ -f2 | tr -d ' ')

echo "使用版本信息: versionName=$VERSION_NAME, buildNumber=$BUILD_NUMBER (flavor: $FLAVOR)"

# 创建 patch.zip 文件
create_patch_zip() {
  local apk_path=$1
  local output_dir=$(dirname "$apk_path")
  local patch_dir="$output_dir/patch_temp"

  echo "正在创建 patch.zip..."

  # 创建临时目录
  rm -rf "$patch_dir"
  mkdir -p "$patch_dir/lib"

  # 解压 APK，使用 -o 参数覆盖所有文件而不提示
  unzip -q -o "$apk_path" -d "$patch_dir/extracted"

  # 复制需要的文件
  cp -r "$patch_dir/extracted/assets" "$patch_dir/"
  cp "$patch_dir/extracted/AndroidManifest.xml" "$patch_dir/"

  # 只复制 libapp.so 文件
  local abi_types=()
  for lib_path in $(find "$patch_dir/extracted/lib" -name "libapp.so"); do
    # 获取相对路径 (例如 arm64-v8a/libapp.so 或 armeabi-v7a/libapp.so)
    rel_path=$(echo "$lib_path" | sed "s|$patch_dir/extracted/lib/||")
    # 提取ABI类型(目录名)
    abi_type=$(dirname "$rel_path")
    abi_types+=("$abi_type")
    # 创建目标目录
    target_dir="$patch_dir/lib/$(dirname "$rel_path")"
    mkdir -p "$target_dir"
    # 复制文件
    cp "$lib_path" "$target_dir/"
  done

  # 创建 zip 文件
  # 1. 创建完整patch包
  cd "$patch_dir"
  local patch_type="full"
  local patch_name="patch-${patch_type}-${FLAVOR}-${CHANNEL}-${VERSION_NAME}-${BUILD_NUMBER}.zip"
  local patch_zip="$output_dir/$patch_name"
  zip -q -r ../"${patch_name}" assets "AndroidManifest.xml" lib

  # 2. 为每个ABI类型创建单独的patch包
  for abi in "${abi_types[@]}"; do
    local abi_patch_name="patch-${abi}-${FLAVOR}-${CHANNEL}-${VERSION_NAME}-${BUILD_NUMBER}.zip"
    zip -q -r ../"${abi_patch_name}" assets "AndroidManifest.xml" "lib/${abi}"
  done

  cd - > /dev/null

  # 清理临时目录
  rm -rf "$patch_dir"

  echo "patch 创建完成: $patch_zip"
}

# 从build.gradle读取签名配置
get_signing_config() {
  local flavor=$1
  local gradle_file="android/app/build.gradle"

  echo "正在从 build.gradle 读取签名配置..."

  # 首先确定使用哪个签名配置
  # 从buildTypes.release.signingConfig读取
  local signing_config_name=$(awk '
    BEGIN { in_build_types = 0; in_release = 0 }
    /buildTypes/ { in_build_types = 1 }
    in_build_types && /release/ && /{/ { in_release = 1; next }
    in_release && /signingConfig/ {
      # 提取signingConfig后面的配置名
      gsub(/.*signingConfigs\./, "", $0);
      gsub(/[^a-zA-Z0-9_].*/, "", $0);
      if (length($0) > 0) {
        print $0;
        exit
      }
    }
    in_release && /^[[:space:]]*}/ { in_release = 0 }
    /^[[:space:]]*}/ && in_build_types { in_build_types = 0 }
  ' "$gradle_file")

  if [ -z "$signing_config_name" ]; then
    echo "警告: 未找到签名配置，将跳过签名步骤"
    return 1
  fi

  echo "使用签名配置: $signing_config_name"

  # 从signingConfigs中读取具体配置
  local signing_configs=$(awk -v config_name="$signing_config_name" '
    BEGIN { in_signing_configs = 0; in_target_config = 0 }
    /signingConfigs/ { in_signing_configs = 1 }
    in_signing_configs && index($0, config_name) && /{/ { in_target_config = 1; next }
    in_target_config && /keyAlias/ {
      gsub(/.*keyAlias[[:space:]]*/, "", $0);
      gsub(/^['\''\"'\'']/, "", $0);
      gsub(/['\''\"'\''].*/, "", $0);
      if (length($0) > 0) print "KEY_ALIAS=" $0;
    }
    in_target_config && /keyPassword/ {
      gsub(/.*keyPassword[[:space:]]*/, "", $0);
      gsub(/^['\''\"'\'']/, "", $0);
      gsub(/['\''\"'\''].*/, "", $0);
      if (length($0) > 0) print "KEY_PASSWORD=" $0;
    }
    in_target_config && /storeFile/ {
      gsub(/.*file[[:space:]]*\([[:space:]]*/, "", $0);
      gsub(/^['\''\"'\'']/, "", $0);
      gsub(/['\''\"'\''].*/, "", $0);
      if (length($0) > 0) print "STORE_FILE=" $0;
    }
    in_target_config && /storePassword/ {
      gsub(/.*storePassword[[:space:]]*/, "", $0);
      gsub(/^['\''\"'\'']/, "", $0);
      gsub(/['\''\"'\''].*/, "", $0);
      if (length($0) > 0) print "KEYSTORE_PASSWORD=" $0;
    }
    in_target_config && /^[[:space:]]*}/ { in_target_config = 0; exit }
    /^[[:space:]]*}/ && in_signing_configs { in_signing_configs = 0 }
  ' "$gradle_file")

  # 解析配置
  eval "$signing_configs"

  # 构建keystore路径
  if [ -n "$STORE_FILE" ]; then
    KEYSTORE_PATH="android/app/$STORE_FILE"
  fi

  # 验证必要的配置是否都已读取
  if [ -z "$KEY_ALIAS" ] || [ -z "$KEY_PASSWORD" ] || [ -z "$KEYSTORE_PATH" ] || [ -z "$KEYSTORE_PASSWORD" ]; then
    echo "警告: 签名配置不完整，将跳过签名步骤"
    echo "  KEY_ALIAS: $KEY_ALIAS"
    echo "  KEYSTORE_PATH: $KEYSTORE_PATH"
    echo "  KEY_PASSWORD: ${KEY_PASSWORD:+已设置}"
    echo "  KEYSTORE_PASSWORD: ${KEYSTORE_PASSWORD:+已设置}"
    return 1
  fi

  # 检查keystore文件是否存在
  if [ ! -f "$KEYSTORE_PATH" ]; then
    echo "警告: 签名文件 $KEYSTORE_PATH 不存在，将跳过签名步骤"
    return 1
  fi

  echo "签名配置读取成功:"
  echo "  Keystore: $KEYSTORE_PATH"
  echo "  Key Alias: $KEY_ALIAS"

  return 0
}

# 对APK进行签名
sign_apk() {
  local apk_path=$1

  if ! get_signing_config "$FLAVOR"; then
    echo "跳过APK签名"
    return 0
  fi

  echo "正在对APK进行签名..."

  # 使用apksigner进行签名
  if command -v apksigner >/dev/null 2>&1; then
    apksigner sign --ks "$KEYSTORE_PATH" \
      --ks-pass pass:"$KEYSTORE_PASSWORD" \
      --ks-key-alias "$KEY_ALIAS" \
      --key-pass pass:"$KEY_PASSWORD" \
      --out "${apk_path%.apk}-signed.apk" \
      "$apk_path"

    if [ $? -eq 0 ]; then
      mv "${apk_path%.apk}-signed.apk" "$apk_path"
      echo "APK签名完成"
    else
      echo "APK签名失败"
      return 1
    fi
  else
    echo "警告: 未找到apksigner工具，跳过APK签名"
  fi
}

# 对AAB进行签名
sign_aab() {
  local aab_path=$1

  if ! get_signing_config "$FLAVOR"; then
    echo "跳过AAB签名"
    return 0
  fi

  echo "正在对AAB进行签名..."

  # 使用jarsigner进行签名
  if command -v jarsigner >/dev/null 2>&1; then
    jarsigner -verbose -sigalg SHA256withRSA -digestalg SHA-256 \
      -keystore "$KEYSTORE_PATH" \
      -storepass "$KEYSTORE_PASSWORD" \
      -keypass "$KEY_PASSWORD" \
      "$aab_path" "$KEY_ALIAS"

    if [ $? -eq 0 ]; then
      echo "AAB签名完成"
    else
      echo "AAB签名失败"
      return 1
    fi
  else
    echo "警告: 未找到jarsigner工具，跳过AAB签名"
  fi
}

# 删除APK/AAB中的flutter_assets目录并重新打包
remove_flutter_assets_and_repackage() {
  local package_path=$1
  local file_ext=$2
  local temp_dir=$(dirname "$package_path")/repackage_temp

  echo "正在删除 flutter_assets 目录并重新打包 $file_ext..."

  # 创建临时目录
  rm -rf "$temp_dir"
  mkdir -p "$temp_dir"

  if [ "$file_ext" = "apk" ]; then
    # 处理APK文件
    # 解压APK
    unzip -q -o "$package_path" -d "$temp_dir"

    # 删除flutter_assets目录
    if [ -d "$temp_dir/assets/flutter_assets" ]; then
      echo "删除 APK 中的 flutter_assets 目录..."
      rm -rf "$temp_dir/assets/flutter_assets"
    else
      echo "APK 中未找到 flutter_assets 目录，跳过删除步骤"
    fi

    # 重新打包APK
    cd "$temp_dir"
    zip -q -r "../$(basename "$package_path")" .
    cd - > /dev/null

    # 替换原文件
    mv "$temp_dir/../$(basename "$package_path")" "$package_path"
    echo "APK 重新打包完成"

    # 对重新打包的APK进行签名
    sign_apk "$package_path"

  elif [ "$file_ext" = "aab" ]; then
    # 处理AAB文件
    # AAB文件是zip格式，但结构不同
    unzip -q -o "$package_path" -d "$temp_dir"

    # 在AAB中，flutter_assets通常在base/assets/目录下
    if [ -d "$temp_dir/base/assets/flutter_assets" ]; then
      echo "删除 AAB 中的 flutter_assets 目录..."
      rm -rf "$temp_dir/base/assets/flutter_assets"
    else
      echo "AAB 中未找到 flutter_assets 目录，跳过删除步骤"
    fi

    # 重新打包AAB
    cd "$temp_dir"
    zip -q -r "../$(basename "$package_path")" .
    cd - > /dev/null

    # 替换原文件
    mv "$temp_dir/../$(basename "$package_path")" "$package_path"
    echo "AAB 重新打包完成"

    # 对重新打包的AAB进行签名
    sign_aab "$package_path"
  fi

  # 清理临时目录
  rm -rf "$temp_dir"
}

# 构建指定 flavor 的包
build_with_flavor() {
  local build_cmd=$1
  local output_type=$2
  local file_ext=$3
  local output_dir=$4

  echo "正在构建 $output_type (flavor: $FLAVOR, channel: $CHANNEL)..."
  flutter $build_cmd --flavor=$FLAVOR --dart-define=CHANNEL="$CHANNEL" --dart-define=VERSION_NAME="$VERSION_NAME" --dart-define=BUILD_NUMBER="$BUILD_NUMBER" --build-name=$VERSION_NAME --build-number=$BUILD_NUMBER

  # 重命名输出文件以包含版本、flavor 和 channel 信息
  local output_file="$output_dir/gleezy-${FLAVOR}-${CHANNEL}-${VERSION_NAME}-${BUILD_NUMBER}.$file_ext"
  # 根据不同的构建类型和flavor，输出文件路径可能不同
  local source_file="$output_dir/app-${FLAVOR}-release.$file_ext"
  if [ ! -f "$source_file" ]; then
    # 尝试旧的路径格式
    source_file="$output_dir/app-release.$file_ext"
  fi
  mv "$source_file" "$output_file"
  echo "$output_type 构建完成: $output_file"

  # 删除flutter_assets目录并重新打包
  remove_flutter_assets_and_repackage "$output_file" "$file_ext"

  # 如果是 APK，创建 patch.zip
  if [ "$file_ext" = "apk" ]; then
    create_patch_zip "$output_file"
  fi
}

# 根据打包类型执行不同的构建命令
case $BUILD_TYPE in
  "apk")
    build_with_flavor "build apk" "APK" "apk" "build/app/outputs/apk/${FLAVOR}/release"
    ;;

  "appbundle"|"aab")
    build_with_flavor "build appbundle" "App Bundle" "aab" "build/app/outputs/bundle/${FLAVOR}Release"
    ;;

  "all")
    build_with_flavor "build apk" "APK" "apk" "build/app/outputs/apk/${FLAVOR}/release"
    build_with_flavor "build appbundle" "App Bundle" "aab" "build/app/outputs/bundle/${FLAVOR}Release"
    ;;

  *)
    echo "错误: 不支持的构建类型 '$BUILD_TYPE'"
    echo "请使用 './build.sh help' 查看支持的打包类型"
    exit 1
    ;;
esac